<?php

namespace App\Model\Erp\Entity;

use App\Model\DTO\Document\DocumentDto;
use App\Model\Erp\Enum\ErpOrderStatus;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\Product\ProductItem;
use Nette\SmartObject;
use stdClass;
use App\Model\Erp\Exception\ErpException;
use App\Model\Erp\Helper\NovikoTransportHelper;
use App\Model\StringHelper;
use function trim;

class ErpOrder
{

	use SmartObject;

	/** @var array list of statuses */
	public array $statuses;

	/**
	 * List of item statuses for order items
	 * saveObjednavkaHD() returns status in <hdItemStatus> but getObjednavkaHD() in <status>
	 *
	 * @var array
	 */
	public static array $itemStatuses = [
		1 => 'OK',
		2 => 'Product does not exist',
		3 => 'Sales restriction',
		4 => 'Product ordered partially',
		5 => 'Not ordered - product not in stock',
		6 => 'Product is cancelled',
	];

	protected string $idObjednavkaPart = 0;

	protected string $adJmeno = '';

	protected string $adPrijmeni = '';

	protected string $adUlice = '';

	protected string $adMesto = '';

	protected string $adPsc = '';

	protected string $adSpolecnost = '';

	protected string $kontMail = '';

	protected string $kontTel = '';

	protected int $kodDopravy;

	protected ?string $doprPobockaVyzv;

	protected string $cisloDl = '';

	protected bool $doruceniDo12h = false;

	protected string $adZedme = 'Česká Republika';

	protected string $adZemeIsoCode = 'CZ';

	/** @var float @note although optional, if missing in xml, it ends in Noviko with SQL error - cash on delivery cannot be NULL */
	protected float $dobirka = 0.0;

	protected string $menaIsoCode = 'CZK';

	/** @var bool false = "reservation" -> unconfirmed order */
	protected bool $uzavrit = false;

	protected string $poznProRidice = '';

	/** @var string format: "U{number of notes}: note number1;note number2;note number3" */
	protected string $poznProSklad = '';

	protected array $polozky;

	protected ?int $status;

	public function __construct()
	{
		// set defaults
		$this->statuses = ErpOrderStatus::getAllStatuses();
		$this->kodDopravy = NovikoTransportHelper::NOVIKO_ID_DEFAULT;
	}

	public function setDataFromResult(stdClass $data): void
	{
		foreach ($data as $prop => $val) {
			if ($val !== null && property_exists($this, $prop)) {
				if ($prop === 'kontTel') {
					$this->$prop = preg_replace('/\s+/', '', $val);
					continue;
				}
				if ($prop === 'polozky') {
					$this->setPolozky($val);
					continue;
				}

				$this->$prop = $val;
			}
		}
	}

	/**
	 * @return stdClass
	 */
	public function getData(): stdClass
	{
		$data = new stdClass();
		foreach ($this as $prop => $val) {
			// if it has NULL we cannot send to Noviko, e.g. doprPobockaVyzv
			if ($val !== null) {
				$data->$prop = $val;
			}

			if ($prop === 'polozky') {
				if (count($val) === 1) {
					$val = $val[0];
				}
				$data->$prop = $val;
			}
		}
		return $data;
	}

	/**
	 * @throws ErpException
	 */
	public function setDataFromOrder(Order $order): void
	{
		$orderDto = new DocumentDto($order);

		// delivery address
		if ($orderDto->useDeliveryAddress) {
			$deliveryAddressDto = $orderDto->deliveryAddress;
		} else {
			$deliveryAddressDto = $orderDto->customerAddress;
		}

		// header
		$this->setIdObjednavkaPart($orderDto->novikoIdObjednavkaPart)
			->setAdJmeno($deliveryAddressDto->firstName)
			->setAdPrijmeni($deliveryAddressDto->lastName)
			->setAdUlice($deliveryAddressDto->street)
			->setAdMesto($deliveryAddressDto->city)
			->setAdPsc($deliveryAddressDto->zipNormalized)
			->setAdSpolecnost($deliveryAddressDto->companyName)
			->setKontMail($order->email)
			->setKontTel($deliveryAddressDto->phoneNumberNormalized)
			->setPoznProSklad($orderDto->noteForStore);

		$this->adZedme = $orderDto->countryName;
		$this->adZemeIsoCode = $orderDto->countryIsoCode;
		$this->menaIsoCode = $orderDto->currencyIsoCode;

		// shipping
		if (empty($orderDto->novikoTransportId)) {
			throw new ErpException('Unknown transport Noviko ID');
		}
		$this->setKodDopravy($orderDto->novikoTransportId);
		if (!empty($orderDto->pickupPointId)) {
			$this->setDoprPobockaVyzv($orderDto->pickupPointId);
		}

		// payment - cash on delivery
		if (!$orderDto->cashOnDeliveryPrice->isZero()) {
			$this->setDobirka($orderDto->cashOnDeliveryPrice->getAmount()->toFloat());
		}

		// items
		// TODO - if it's a package, break it down into items
		foreach ($order->getItems() as $orderItem) {
			// iterate through all items = everything with Noviko ID goes to Noviko (product, gift, sample, present)
			if ($orderItem instanceof ProductItem) {
				if (empty($orderItem->variant?->extId)) {
					continue;
				}
				$erpItem = new stdClass();
				$erpItem->idItemPart = $orderItem->id; // java.doc: Partner's item line ID
				$erpItem->kodZbo = $orderItem->variant->extId;
				$erpItem->pocet = $orderItem->amount;
				$this->polozky[] = $erpItem;
			}
		}

		// other
		$this->setCisloDl($orderDto->orderNumber);

		$this->setUzavrit($order->readyToCloseInNoviko);
	}

	public function setIdObjednavkaPart(string $idObjednavkaPart): static
	{
		$this->idObjednavkaPart = $idObjednavkaPart;
		return $this;
	}

	public function setAdJmeno(string $adJmeno): static
	{
		$this->adJmeno = self::fixEncodingTranslit($adJmeno);
		return $this;
	}

	public function setAdPrijmeni(string $adPrijmeni): static
	{
		$this->adPrijmeni = self::fixEncodingTranslit($adPrijmeni);
		return $this;
	}

	public function setAdUlice(string $adUlice): static
	{
		$this->adUlice = self::fixEncodingTranslit($adUlice);
		return $this;
	}

	public function setAdMesto(string $adMesto): static
	{
		$this->adMesto = self::fixEncodingTranslit($adMesto);
		return $this;
	}

	public function setAdPsc(string $adPsc): static
	{
		$this->adPsc = self::fixEncodingTranslit($adPsc);
		return $this;
	}

	public function setAdSpolecnost(string $adSpolecnost): static
	{
		$this->adSpolecnost = self::fixEncodingTranslit($adSpolecnost);
		return $this;
	}

	public function setKontMail(string $kontMail): static
	{
		$this->kontMail = $kontMail;
		return $this;
	}

	public function setKontTel(string $kontTel): static
	{
		$this->kontTel = (string) $kontTel;
		return $this;
	}

	public function setKodDopravy(int $kodDopravy): static
	{
		$this->kodDopravy = $kodDopravy;
		return $this;
	}

	public function setDoprPobockaVyzv(mixed $doprPobockaVyzv): static
	{
		if (empty($doprPobockaVyzv)) {
			$this->doprPobockaVyzv = null;
		} elseif (is_string($doprPobockaVyzv)) {
			$this->doprPobockaVyzv = (string) $doprPobockaVyzv;
		} else {
			$this->doprPobockaVyzv = (int) $doprPobockaVyzv;
		}

		return $this;
	}

	public function setCisloDl(string $cisloDl): static
	{
		$this->cisloDl = $cisloDl;
		return $this;
	}

	public function setDoruceniDo12h(bool $doruceniDo12h): static
	{
		$this->doruceniDo12h = $doruceniDo12h;
		return $this;
	}

	public function setDobirka(float $dobirka): static
	{
		$this->dobirka = $dobirka;
		return $this;
	}

	public function setUzavrit(bool $uzavrit): static
	{
		$this->uzavrit = $uzavrit;
		return $this;
	}

	public function setPoznProRidice(string $poznProRidice): static
	{
		$this->poznProRidice = trim($poznProRidice);
		return $this;
	}

	public function setPoznProSklad(string $poznProSklad): static
	{
		$this->poznProSklad = trim($poznProSklad);
		return $this;
	}

	public function setStatus(int $status): static
	{
		$this->status = StringHelper::intToNull($status);
		return $this;
	}

	public function getIdObjednavkaPart(): int
	{
		return $this->idObjednavkaPart;
	}

	public function getAdJmeno(): string
	{
		return $this->adJmeno;
	}

	public function getAdPrijmeni(): string
	{
		return $this->adPrijmeni;
	}

	public function getAdUlice(): string
	{
		return $this->adUlice;
	}

	public function getAdMesto(): string
	{
		return $this->adMesto;
	}

	public function getAdPsc(): string
	{
		return $this->adPsc;
	}

	public function getAdSpolecnost(): string
	{
		return $this->adSpolecnost;
	}

	public function getKontMail(): string
	{
		return $this->kontMail;
	}

	public function getKontTel(): string
	{
		return $this->kontTel;
	}

	public function getKodDopravy(): int
	{
		return $this->kodDopravy;
	}

	public function getDoprPobockaVyzv(): ?string
	{
		return $this->doprPobockaVyzv;
	}

	public function getCisloDl(): string
	{
		return $this->cisloDl;
	}

	public function getDoruceniDo12h(): bool
	{
		return $this->doruceniDo12h;
	}

	public function getAdZedme(): string
	{
		return $this->adZedme;
	}

	public function getAdZemeIsoCode(): string
	{
		return $this->adZemeIsoCode;
	}

	public function getDobirka(): float
	{
		return $this->dobirka;
	}

	public function getMenaIsoCode(): string
	{
		return $this->menaIsoCode;
	}

	public function getUzavrit(): bool
	{
		return $this->uzavrit;
	}

	public function getPoznProRidice(): string
	{
		return $this->poznProRidice;
	}

	public function getPoznProSklad(): string
	{
		return $this->poznProSklad;
	}

	public function getPolozky(): array
	{
		return $this->polozky;
	}

	public function getStatus(): ?int
	{
		return $this->status;
	}

	public function setPolozky(stdClass|array $polozky): static
	{
		$this->polozky = $polozky instanceof stdClass ? [0 => $polozky] : (array) $polozky;
		return $this;
	}

	/**
	 * Prevede (prevypravi) nepovolene znaky ve windows-1250 do UTF-8
	 * http://phpfashion.com/prevody-mezi-kodovanim
	 *
	 * @param string $s
	 * @return string|false
	 */
	public static function fixEncodingTranslit(string $s): string
	{
		$s = @iconv('UTF-8', 'WINDOWS-1250//TRANSLIT', $s);
		$s = @iconv('WINDOWS-1250', 'UTF-8', (string) $s);
		return (string) $s;
	}

}
