<?php

declare(strict_types=1);

namespace App\Model\Erp\Enum;

use App\Model\Orm\Traits\EnumToArray;

enum ErpOrderStatus: int
{

	use EnumToArray; //phpcs:ignore

	case Storno = -10; // Stornovaná
	case Merge = -5; // Sloučená
	case Incomplete = -1; // Nekompletní
	case Created = 0; // Vytvořená
	case Open = 1; // Neuzavřená (Rezervovaná)
	case DelayingStock = 11; // Ve skladu - odložená
	case DelayingComplete = 14; // Odložená kompletace
	case ToComplete = 15; // Ke kompletaci
	case ClosedSupplier = 16; // Objednávka je již u dodavatele uzavřena
	case DelayingDelivery = 19; // Odložení předání dopravci
	case ToDelivery = 20; // K předání dopravci
	case ForwardedToDelivery = 21; // Předáno dopravci
	case ReturnedByCarrier = 22; // Vráceno dopravcem
	case Delivered = 29; // Expedovaná
	case StornoBefore = 101; // Uzavřeno jinak

	/**
	 * Speciální hodnota pro neodeslanou objednávku (původně null)
	 */
	public static function getUnsentStatus(): null
	{
		return null;
	}

	/**
	 * Získá všechny statusy včetně speciální hodnoty Unsent
	 */
	public static function getAllStatuses(): array
	{
		$statuses = [];
		$statuses[self::getUnsentStatus()] = 'Unsent';

		foreach (self::cases() as $case) {
			$statuses[$case->value] = $case->getLabel();
		}

		return $statuses;
	}

	public function getLabel(): string
	{
		return match ($this) {
			self::Storno => 'Storno',
			self::Merge => 'Merge',
			self::Incomplete => 'Incomplete',
			self::Created => 'Created',
			self::Open => 'Open',
			self::DelayingStock => 'Delaying stock',
			self::DelayingComplete => 'Delaying complete',
			self::ToComplete => 'To complete',
			self::ClosedSupplier => 'Closed supplier',
			self::DelayingDelivery => 'Delaying delivery',
			self::ToDelivery => 'To delivery',
			self::ForwardedToDelivery => 'Forwarded to delivery',
			self::ReturnedByCarrier => 'Returned back by carrier',
			self::Delivered => 'Delivered',
			self::StornoBefore => 'Storno before',
		};
	}

}
