<?php declare(strict_types=1);

namespace App\Model\DTO\Document;

use App\Model\Erp\Helper\NovikoTransportHelper;
use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\Payment\PaymentType;
use App\Model\Orm\Order\Product\ProductItem;
use Brick\Money\Money;

class DocumentDto
{

	public const string TYPE_ORDER = 'order';
	public const string TYPE_INVOICE = 'invoice';
	public const string TYPE_CREDIT_NOTE = 'credit_note';

	public AddressDto $customerAddress;

	public AddressDto $deliveryAddress;

	public bool $useDeliveryAddress;

	public string $number;

	public string $orderNumber;

	public string $novikoIdObjednavkaPart;

	public string $noteForStore;

	public string $note;

	public string $countryName;

	public string $countryIsoCode;

	public string $currencyIsoCode;

	public int $novikoTransportId;

	public ?string $pickupPointId;

	public bool $isCashOnDelivery;

	public Money $cashOnDeliveryPrice;

	public array $items;

	public function __construct(
		Order $order,
	)
	{
		$this->customerAddress = new AddressDto(
			$order->name,
			$order->street,
			$order->city,
			$order->zip,
			$order->state->name,
			$order->phone,
			$order->companyName,
			$order->companyIdentifier,
			$order->vatNumber,
		);

		$this->useDeliveryAddress = $order->useDeliveryAddress();
		if ($this->useDeliveryAddress) {
			$deliveryInformation = $order->delivery->information;
			$this->deliveryAddress = new AddressDto(
				$deliveryInformation->name ?? null,
				$deliveryInformation->street ?? null,
				$deliveryInformation->city ?? null,
				$deliveryInformation->zip ?? null,
				$deliveryInformation->state?->name ?? null,
				$deliveryInformation->phoneNumber ?? $order->phone,
				$deliveryInformation->company ?? null,
				null,
				null,
			);

		}

		$this->orderNumber = $order->orderNumber;
		$this->novikoIdObjednavkaPart = $order->novikoIdObjednavkaPart;

		$unionNotesForDriver = [];
		//TODO ?Honza - kde se ukládá poznímka pro řidiče? ->note je špatně
		if ($this->useDeliveryAddress && !empty($order->note)) {
			$unionNotesForDriver[] = 'DA: ' . $order->note;
		}
		if (!empty($order->note)) {
			$unionNotesForDriver[] = 'FA: ' . $order->note;
		}
		$this->noteForStore = implode(PHP_EOL, $unionNotesForDriver);

		$state = $order->mutation->getFirstState();
		$this->countryName = $state->name;
		$this->countryIsoCode = $order->country->code;
		$this->currencyIsoCode = $order->currency->getCurrencyCode();

		$this->isCashOnDelivery = $order->payment?->information->type === PaymentType::CashOnDelivery;
		if ($this->isCashOnDelivery) {
			$this->cashOnDeliveryPrice = $order->getTotalPriceWithDeliveryVat();
		} else {
			$this->cashOnDeliveryPrice = Money::of(0, $order->currency->getCurrencyCode());
		}

		$this->novikoTransportId = NovikoTransportHelper::getNovikoId($order->delivery->deliveryMethod->getDeliveryMethod());

		$this->pickupPointId = $order->getPickupPointId();

		$items = [];
		foreach ($order->getItems() as $orderItem) {
			if ($orderItem instanceof ProductItem) {
				$items[$orderItem->id] = new DocumentItemDto($orderItem);
			}
		}
		$this->items = $items;
	}

}
